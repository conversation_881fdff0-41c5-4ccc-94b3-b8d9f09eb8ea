from django.db import models


class Safety(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    systemname = models.CharField(db_column='SystemName', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    safetyfeature = models.CharField(db_column='SafetyFeature', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    safetystandard = models.CharField(db_column='SafetyStandard', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    risklevel = models.CharField(db_column='RiskLevel', max_length=1, db_collation='Thai_CI_AI')  # Field name made lowercase.
    lastauditdate = models.DateTimeField(db_column='LastAuditDate', blank=True, null=True)  # Field name made lowercase.
    compliancestatus = models.CharField(db_column='ComplianceStatus', max_length=200, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    remark = models.TextField(db_column='Remark', db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    createuserid = models.IntegerField(db_column='CreateUserId')  # Field name made lowercase.
    createdate = models.DateTimeField(db_column='CreateDate')  # Field name made lowercase.
    updateuserid = models.IntegerField(db_column='UpdateUserId', blank=True, null=True)  # Field name made lowercase.
    updatedate = models.DateTimeField(db_column='UpdateDate', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = 'Safety'

